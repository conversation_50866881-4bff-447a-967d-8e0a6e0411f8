<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>折扇图形绘制</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        .container {
            text-align: center;
        }
        canvas {
            border: 1px solid #ccc;
            background-color: white;
            margin: 20px 0;
        }
        .controls {
            margin: 20px 0;
        }
        input[type="file"] {
            margin: 10px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>折扇图形绘制</h1>
        <div class="controls">
            <input type="file" id="imageInput" accept="image/*">
            <button onclick="drawFan()">绘制折扇</button>
            <button onclick="clearCanvas()">清空画布</button>
        </div>
        <canvas id="fanCanvas" width="800" height="600"></canvas>
    </div>

    <script>
        const canvas = document.getElementById('fanCanvas');
        const ctx = canvas.getContext('2d');
        let selectedImage = null;

        // 处理图片上传
        document.getElementById('imageInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    const img = new Image();
                    img.onload = function() {
                        selectedImage = img;
                        drawFan();
                    };
                    img.src = event.target.result;
                };
                reader.readAsDataURL(file);
            }
        });

        function drawFan() {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 折扇参数
            const centerX = canvas.width / 2;
            const centerY = canvas.height - 50;
            const fanRadius = 300;
            const fanAngle = Math.PI * 0.8; // 144度
            const startAngle = -fanAngle / 2;
            const endAngle = fanAngle / 2;
            const ribCount = 15; // 扇骨数量
            const handleLength = 80; // 扇柄长度

            // 如果有选择的图片，先绘制图片填充
            if (selectedImage) {
                drawImageInFan(centerX, centerY, fanRadius, startAngle, endAngle);
            }

            // 绘制折扇轮廓
            drawFanOutline(centerX, centerY, fanRadius, startAngle, endAngle, ribCount, handleLength);
        }

        function drawImageInFan(centerX, centerY, radius, startAngle, endAngle) {
            // 创建扇形裁剪路径
            ctx.save();
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, startAngle, endAngle);
            ctx.closePath();
            ctx.clip();

            // 计算图片绘制位置和大小
            const fanWidth = radius * Math.sin(endAngle) * 2;
            const fanHeight = radius * (1 - Math.cos(endAngle));
            
            // 绘制图片
            ctx.drawImage(
                selectedImage,
                centerX - fanWidth / 2,
                centerY - radius,
                fanWidth,
                fanHeight
            );
            
            ctx.restore();
        }

        function drawFanOutline(centerX, centerY, radius, startAngle, endAngle, ribCount, handleLength) {
            ctx.strokeStyle = '#8B4513';
            ctx.lineWidth = 2;

            // 绘制扇面外弧
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, startAngle, endAngle);
            ctx.stroke();

            // 绘制扇骨
            for (let i = 0; i <= ribCount; i++) {
                const angle = startAngle + (endAngle - startAngle) * (i / ribCount);
                const x1 = centerX + Math.cos(angle) * handleLength;
                const y1 = centerY + Math.sin(angle) * handleLength;
                const x2 = centerX + Math.cos(angle) * radius;
                const y2 = centerY + Math.sin(angle) * radius;

                ctx.beginPath();
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                
                // 主扇骨（两端和中间）使用更粗的线条
                if (i === 0 || i === ribCount || i === Math.floor(ribCount / 2)) {
                    ctx.lineWidth = 4;
                    ctx.strokeStyle = '#654321';
                } else {
                    ctx.lineWidth = 2;
                    ctx.strokeStyle = '#8B4513';
                }
                ctx.stroke();
            }

            // 绘制扇柄部分的连接弧
            ctx.beginPath();
            ctx.strokeStyle = '#654321';
            ctx.lineWidth = 3;
            ctx.arc(centerX, centerY, handleLength, startAngle, endAngle);
            ctx.stroke();

            // 绘制扇面边缘（左右两条直线）
            ctx.beginPath();
            ctx.strokeStyle = '#8B4513';
            ctx.lineWidth = 2;
            
            // 左边缘
            ctx.moveTo(centerX + Math.cos(startAngle) * handleLength, centerY + Math.sin(startAngle) * handleLength);
            ctx.lineTo(centerX + Math.cos(startAngle) * radius, centerY + Math.sin(startAngle) * radius);
            
            // 右边缘
            ctx.moveTo(centerX + Math.cos(endAngle) * handleLength, centerY + Math.sin(endAngle) * handleLength);
            ctx.lineTo(centerX + Math.cos(endAngle) * radius, centerY + Math.sin(endAngle) * radius);
            
            ctx.stroke();
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // 初始绘制一个空的折扇轮廓
        drawFan();
    </script>
</body>
</html>
